package com.quantchi.knowledge.center.dao.mysql;

import com.quantchi.knowledge.center.bean.bo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface IndustryRankListMapper {

    /**
     * 获取patent_applicant表所有企业的授权专利数
     *
     * @return
     */
    List<IndustryRankRdStrengthBO> getCompanyPatentAccreditCountList();

    /**
     * 获取指定企业的专利总量
     *
     * @param cid
     * @return
     */
    Integer getCompanyPatentTotalCount(@Param("cid") String cid);

    /**
     * 投资潜力榜
     *
     * @param province
     * @param city
     * @param childNodeIdList
     * @param limit
     * @return
     */
    List<IndustryRankInvestPotentialBO> getInvestPotentialList(@Param("province") String province,
                                                               @Param("city") String city,
                                                               @Param("childNodeIdList") List<String> childNodeIdList,
                                                               @Param("limit") Integer limit);


    /**
     * 资本青睐榜
     *
     * @param province
     * @param city
     * @param childNodeIdList
     * @param limit
     * @return
     */
    List<IndustryRankCapitalPreferenceBO> getCapitalPreferenceList(@Param("province") String province,
                                                                   @Param("city") String city,
                                                                   @Param("childNodeIdList") List<String> childNodeIdList,
                                                                   @Param("limit") Integer limit);

    /**
     * 对外投资榜-直接投资
     *
     * @param province
     * @param city
     * @param childNodeIdList
     * @param limit
     * @return
     */
    List<IndustryRankOutwardInvestBO> getOutwardDirectInvestList(@Param("province") String province,
                                                                 @Param("city") String city,
                                                                 @Param("childNodeIdList") List<String> childNodeIdList,
                                                                 @Param("limit") Integer limit);

    /**
     * 对外投资榜-控股企业数量
     *
     * @param cidList
     * @return
     */
    List<IndustryRankOutwardInvestBO> getHoldingCompanyCountList(@Param("cidList") List<String> cidList);

    /**
     * 营收规模榜、营收利润榜、链主企业榜
     *
     * @param type
     * @param province
     * @param city
     * @param childNodeIdList
     * @param limit
     * @return
     */
    List<IndustryRankAnnualReportBO> getAnnualReportList(@Param("type") Integer type,
                                                         @Param("province") String province,
                                                         @Param("city") String city,
                                                         @Param("childNodeIdList") List<String> childNodeIdList,
                                                         @Param("limit") Integer limit);
}
