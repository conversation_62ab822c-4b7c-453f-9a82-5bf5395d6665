package com.quantchi.knowledge.center.service;

import com.quantchi.knowledge.center.bean.bo.IndustryRankRdStrengthBO;

import java.util.List;

/**
 * 专利排名服务接口
 * 用于实现新的专利数量榜计算逻辑
 */
public interface PatentRankingService {

    /**
     * 根据产业链节点获取专利排名
     * 
     * @param chainId 产业链ID
     * @param nodeIdList 节点ID列表
     * @param regionName 地区名称
     * @param type 类型：企业或高校，默认为企业
     * @return 专利排名列表
     */
    List<IndustryRankRdStrengthBO> getPatentRankingByNode(String chainId, List<String> nodeIdList, String regionName, String type);

}
