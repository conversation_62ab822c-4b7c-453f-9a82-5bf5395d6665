package com.quantchi.knowledge.center.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.knowledge.center.bean.bo.*;
import com.quantchi.knowledge.center.bean.constant.Constants;
import com.quantchi.knowledge.center.bean.entity.DmDivision;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.bean.export.*;
import com.quantchi.knowledge.center.bean.model.IndustryRankListQuery;
import com.quantchi.knowledge.center.bean.vo.IndustryAnalysisPatentApplyVO;
import com.quantchi.knowledge.center.bean.vo.IndustryRankListVO;
import com.quantchi.knowledge.center.bean.vo.NameCountVO;
import com.quantchi.knowledge.center.constants.Constant;
import com.quantchi.knowledge.center.dao.mysql.DmDivisionMapper;
import com.quantchi.knowledge.center.dao.mysql.IndustryOverviewMapper;
import com.quantchi.knowledge.center.dao.mysql.IndustryRankListMapper;
import com.quantchi.knowledge.center.dao.mysql.NodeMapper;
import com.quantchi.knowledge.center.service.INodeService;
import com.quantchi.knowledge.center.service.PatentRankingService;
import com.quantchi.knowledge.center.service.IndustryOverviewService;
import com.quantchi.knowledge.center.service.cache.NodeCacheService;
import com.quantchi.knowledge.center.util.CustomHandler;
import com.quantchi.knowledge.center.util.ExcelStyleUtil;
import com.quantchi.knowledge.center.util.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.quantchi.knowledge.center.config.MyBatisPlusConfig.DYNAMIC_TABLE_NAME_THREAD_LOCAL;
import static com.quantchi.knowledge.center.constants.Constant.NEWEST_YEAR;
import static com.quantchi.knowledge.center.util.LocalFileUtil.getActualFilePath;
import static com.quantchi.knowledge.center.util.MineUtil.YI;

@Service
@Slf4j
@RequiredArgsConstructor
public class IndustryOverviewServiceImpl implements IndustryOverviewService {
    private final IndustryOverviewMapper industryOverviewMapper;
    private final INodeService nodeService;
    private final NodeMapper nodeMapper;
    private final IndustryRankListMapper industryRankListMapper;
    @Autowired
    private PatentRankingService patentRankingService;
    private final DmDivisionMapper dmDivisionMapper;
    private final ThreadPoolTaskExecutor docExecutor;
    private final NodeCacheService nodeCacheService;

    @Override
    public Map<String, Object> industryOverview(final String chainId) {
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        final int size = 5;
        final Map<String, Object> map = new ConcurrentHashMap<>(6);
        final List<CompletableFuture<Void>> futureList = new ArrayList<>(size);
        futureList.add(CompletableFuture.runAsync(() -> {
            map.put("企业总量", nodeCacheService.getNodeCompanyCount(chainId));
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
            final Integer listedCompanyCount = industryOverviewMapper.getListedCompanyCount();
            map.put("上市企业数量", listedCompanyCount);
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
            final int patentCount = industryOverviewMapper.getPatentCountByType(null);
            map.put("专利总量", patentCount);
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
            final int accreditPatentCount = industryOverviewMapper.getPatentCountByType(Collections.singletonList("发明授权"));
            map.put("发明授权数量", accreditPatentCount);
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
            final int year = NEWEST_YEAR - 4;
            final OverviewFinanceValueBO bo = industryOverviewMapper.getFinanceValue(year + "-01-01", NEWEST_YEAR + "-12-31");
            BigDecimal money = bo.getMoney();
            money = money.divide(YI, 1, RoundingMode.HALF_UP);
            map.put("近5年融资金额", money);
            map.put("近5年融资数量", bo.getNum());
        }, docExecutor));
        final CompletableFuture<Void> allFutures =
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        try {
            allFutures.get();
        } catch (final Exception e) {
            log.error(e.getMessage(), e);
        }
        return map;
    }

    @Override
    public List<OverviewCompanyChangeBO> getOverviewCompanyChange(final String chainId) {
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        final List<OverviewCompanyChangeBO> list = industryOverviewMapper.getOverviewCompanyChange();
        final List<OverviewCompanyChangeBO> newlist = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            if (i > 0) {
                final int x = (list.get(i).getNum() - list.get(i - 1).getNum()) * 100;
                final BigDecimal value = BigDecimal.valueOf(list.get(i - 1).getNum());
                final BigDecimal rate;
                if (value.compareTo(BigDecimal.ZERO) != 0) {
                    rate = BigDecimal.valueOf(x)
                            .divide(value, 1, RoundingMode.HALF_UP);
                } else {
                    rate = BigDecimal.ZERO;
                }
                list.get(i).setRate(rate);
                newlist.add(list.get(i));
            }
        }
        return newlist;
    }

    @Override
    public List<OverviewCompanyNodeBO> getCompanyNode(final String chainId) {
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        final List<NodeBO> nolist = industryOverviewMapper.getLevel(1);
        final List<OverviewCompanyNodeBO> list = new ArrayList<>();
        int cot = 0;
        for (final NodeBO node : nolist) {
            final OverviewCompanyNodeBO bo = new OverviewCompanyNodeBO();
            final int count = industryOverviewMapper.getCompanyNodeCount(nodeService.getChildNodeIdList(chainId, node.getId()));
            bo.setNode(node.getName());
            bo.setNum(count);
            cot = cot + count;
            list.add(bo);
        }
        for (final OverviewCompanyNodeBO bo : list) {
            final int x = bo.getNum() * 100;
            if (cot == 0) {
                bo.setRate(BigDecimal.ZERO);
            } else {
                final BigDecimal rate = BigDecimal.valueOf(x).divide(BigDecimal.valueOf(cot), 1, RoundingMode.HALF_UP);
                bo.setRate(rate);
            }
        }
        Collections.sort(list, (o1, o2) -> Integer.compare(o2.getNum(), o1.getNum()));
        return list;
    }

    @Override
    public Map<String, Object> overviewRegion(final String chainId) {
        final Map<String, Object> map = new ConcurrentHashMap<>(8);
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        final int companyCount = industryOverviewMapper.getCompanyCount();
        final List<CompletableFuture<Void>> futureList = new ArrayList<>(2);
        futureList.add(CompletableFuture.runAsync(() -> {
            DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
            //城市的数据
            List<OverviewCompanyRegionBO> cityList = industryOverviewMapper.getRegionList(1);
            if (cityList.size() < 5) {
                return;
            }
            cityList = cityList.subList(0, 5);
            final List<String> cityIdList = cityList.stream().map(regionBO -> "division/" + regionBO.getRegion()).collect(Collectors.toList());
            final List<DmDivision> cityDivisions = dmDivisionMapper.selectBatchIds(cityIdList);
            final Map<String, String> cityCodeMap = cityDivisions.stream().collect(Collectors.toMap(DmDivision::getCode, DmDivision::getName));
            int cityNum = 0;
            for (final OverviewCompanyRegionBO bo : cityList) {
                bo.setRegion(cityCodeMap.getOrDefault(bo.getRegion(), bo.getRegion()));
                cityNum = cityNum + bo.getNum();
            }
            map.put("城市TOP5排行", cityList);
            final BigDecimal cityzb = new BigDecimal(cityNum * 100).divide(new BigDecimal(companyCount), 1, RoundingMode.HALF_UP);
            map.put("城市占比", cityzb);
        }, docExecutor));
        futureList.add(CompletableFuture.runAsync(() -> {
            DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
            // 省份数据
            List<OverviewCompanyRegionBO> provinceList = industryOverviewMapper.getRegionList(2);
            if (provinceList.size() < 5) {
                return;
            }
            //省份的图
            provinceList = provinceList.subList(0, 5);
            final List<String> provinceIdList = provinceList.stream().map(regionBO -> "division/" + regionBO.getRegion()).collect(Collectors.toList());
            final List<DmDivision> provinceDivisions = dmDivisionMapper.selectBatchIds(provinceIdList);
            final Map<String, String> provinceCodeMap = provinceDivisions.stream().collect(
                    Collectors.toMap(DmDivision::getCode, DmDivision::getName));
            int provinceNum = 0;
            for (final OverviewCompanyRegionBO bo : provinceList) {
                bo.setRegion(provinceCodeMap.getOrDefault(bo.getRegion(), bo.getRegion()));
                provinceNum = provinceNum + bo.getNum();
            }
            map.put("省份TOP5排行", provinceList);
            final BigDecimal provincezb = new BigDecimal(provinceNum * 100).divide(new BigDecimal(companyCount), 1, RoundingMode.HALF_UP);
            map.put("省份占比", provincezb);
        }, docExecutor));
        final CompletableFuture<Void> allFutures =
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[2]));
        try {
            allFutures.get();
        } catch (final Exception e) {
            log.error(e.getMessage(), e);
        }
        return map;
    }


    @Override
    public List<OverviewCompanyTypeBO> getCompanyType(final String chainId) {
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        final List<NameCountVO> allTagList = nodeMapper.aggTagWithoutRegion(null);
        final List<OverviewCompanyTypeBO> list = allTagList.stream().map(OverviewCompanyTypeBO::fromNameCountVO)
                .collect(Collectors.toList());
        final List<OverviewCompanyTypeBO> newlist = new ArrayList<>();
        final Set<String> set = Constant.TAGSET;
        for (final OverviewCompanyTypeBO bo : list) {
            if (set.contains(bo.getType())) {
                newlist.add(bo);
            }
        }
        Collections.sort(newlist, (o1, o2) -> o2.getNum().compareTo(o1.getNum()));
        return newlist;
    }

    @Override
    public List<IndustryAnalysisPatentApplyVO> getOveriewPatentApply(final String chainId) {
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        final List<IndustryAnalysisPatentApplyVO> applylist = industryOverviewMapper.getOveriewPatentApply();
        if (!CollectionUtils.isEmpty(applylist)) {
            //计算发明授权同比
            final IndustryAnalysisPatentApplyVO ratioVO = new IndustryAnalysisPatentApplyVO();
            ratioVO.setPatentType("发明授权同比");
            final List<IndustryAnalysisPatentApplyBO> ratioList = new ArrayList<>();
            ratioVO.setList(ratioList);
            final IndustryAnalysisPatentApplyVO vo = applylist.get(0);//发明授权
            final List<IndustryAnalysisPatentApplyBO> list = vo.getList();
            if (!CollectionUtils.isEmpty(list)) {
                for (int i = 0; i + 1 < list.size(); i++) {
                    final IndustryAnalysisPatentApplyBO bo1 = list.get(i);
                    final IndustryAnalysisPatentApplyBO bo2 = list.get(i + 1);

                    final IndustryAnalysisPatentApplyBO boNew = new IndustryAnalysisPatentApplyBO();
                    boNew.setYear(bo2.getYear());
                    if (bo1.getCount() != null && bo2.getCount() != null && bo1.getCount() != 0) {
                        boNew.setNewCount((new BigDecimal(bo2.getCount())
                                .subtract(new BigDecimal(bo1.getCount())))
                                .divide(new BigDecimal(bo1.getCount()), 3, RoundingMode.HALF_UP)
                                .multiply(new BigDecimal(100))
                                .setScale(1, RoundingMode.HALF_UP));
                    } else {
                        boNew.setNewCount(new BigDecimal("0.0"));
                    }
                    ratioList.add(boNew);
                }
                //删掉发明授权最开始的一年，因为第一年算不了同比（测试要求第一年也要算同比）
                list.remove(0);
            }
            applylist.add(ratioVO);
        }

        return applylist;
    }

    @Override
    public List<OverviewPatentDomainBO> getPatentDomain(final String chainId) {
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        return industryOverviewMapper.getPatentDomain();
    }

    @Override
    public List<OverviewFinancingNodeBO> getFinancingNode(final String chainId) {
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        final List<NodeBO> nolist = industryOverviewMapper.getLevel(2);
        final List<OverviewFinancingNodeBO> list = new ArrayList<>();
        for (final NodeBO nbo : nolist) {
            final OverviewFinancingNodeBO bo = industryOverviewMapper.getFinancingNode(nodeService.getChildNodeIdList(chainId, nbo.getId()));
            final BigDecimal money = new BigDecimal(bo.getMoney() != null ? bo.getMoney() : "0")
                    .divide(YI, 1, RoundingMode.HALF_UP);
            bo.setMoney(String.valueOf(money));
            bo.setNode(nbo.getName());
            list.add(bo);
        }
        return list;
    }

    @Override
    public List<OverviewOperateRevenueBO> getOperateRevenue(final String chainId) {
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        //营收
        final List<OverviewOperateRevenueBO> list = industryOverviewMapper.getOperateRevenue();
//        for (OverviewOperateRevenueBO bo : list) {
//            if (bo.getRevenue() != null) {
//                bo.setRevenue(bo.getRevenue().divide(WAN, 1, RoundingMode.HALF_UP));
//            }
//            if (bo.getRevenueRate() != null) {
//                bo.setRevenueRate(bo.getRevenueRate().setScale(2, RoundingMode.HALF_UP));
//            }
//        }
        //计算增速
        if (!CollectionUtils.isEmpty(list)) {
            list.get(0).setRevenueRate(null);
            for (int i = 0; i + 1 < list.size(); i++) {
                final OverviewOperateRevenueBO bo1 = list.get(i);
                final OverviewOperateRevenueBO bo2 = list.get(i + 1);

                if (bo1.getRevenue() != null && !bo1.getRevenue().equals(new BigDecimal("0.00")) && bo2.getRevenue() != null) {
                    bo2.setRevenueRate(((bo2.getRevenue().subtract(bo1.getRevenue()))
                            .divide(bo1.getRevenue(), 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal(100))
                            .setScale(2, RoundingMode.HALF_UP)));
                }
            }
        }

        return list;
    }

    @Override
    public Map<String, List<CommonDataForCountBO>> getOperateProfit(final String chainId) {
        final Map<String, List<CommonDataForCountBO>> result = new HashMap<>();

        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        //毛利率 = sum(利润总额 total_profit) / sum(主营业务收入 main_income)
        //净利率 = sum(净利润 net_profit) / sum(主营业务收入 main_income)
        final List<CommonDataForCountBO> grossProfitMargin = industryOverviewMapper.getGrossProfitMargin();
        final List<CommonDataForCountBO> netProfitMargin = industryOverviewMapper.getNetProfitMargin();

        result.put("grossProfitMargin", grossProfitMargin);
        result.put("netProfitMargin", netProfitMargin);
        return result;
    }

    @Override
    public Map<String, List<OverviewOperateRoeBO>> getOperateRoe(final String chainId) {
        final Map<String, List<OverviewOperateRoeBO>> result = new HashMap<>();
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        //ROE = sum(净利润net_profit)/sum(净资产 = 资产总额 total_assets - 负债总额 total_liabilty)
        final List<OverviewOperateRoeBO> roeList = industryOverviewMapper.getOperateRoe();
        //ROA = sum(净利润net_profit)/sum(资产总额total_assets)
        final List<OverviewOperateRoeBO> roaList = industryOverviewMapper.getOperateRoa();
        result.put("ROE", roeList);
        result.put("ROA", roaList);
        return result;
    }

    @Override
    public List<OverviewDeviationBO> getDeviation(final String chainId) {
        return Collections.emptyList();
//        String deviationTableName = chainId + "_deviation";
//        String nodeTableName = chainId + "_node";
//        List<OverviewDeviationBO> list = industryOverviewMapper.getDeviation(deviationTableName, nodeTableName);
//        return list;
    }

    @Override
    public Map<String, List<CommonDataForCountBO>> getNetProfit(final String chainId) {
        final Map<String, List<CommonDataForCountBO>> result = new HashMap<>();

        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        //净利润
        final List<CommonDataForCountBO> netProfitList = industryOverviewMapper.getNetProfit();
        //计算增速
        final List<CommonDataForCountBO> ratioList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(netProfitList)) {
            //先补齐初始年份，以防前端柱图和折线图对不齐
            final CommonDataForCountBO initialBO = new CommonDataForCountBO();
            initialBO.setName(netProfitList.get(0).getName());
            ratioList.add(initialBO);
            for (int i = 0; i + 1 < netProfitList.size(); i++) {
                final CommonDataForCountBO bo1 = netProfitList.get(i);
                final CommonDataForCountBO bo2 = netProfitList.get(i + 1);

                final CommonDataForCountBO bo = new CommonDataForCountBO();
                bo.setName(bo2.getName());
                if (!StringUtils.isEmpty(bo1.getData()) && !StringUtils.isEmpty(bo2.getData()) && !"0.00".equals(bo1.getData())) {
                    bo.setData((new BigDecimal(bo2.getData()).subtract(new BigDecimal(bo1.getData())))
                            .divide(new BigDecimal(bo1.getData()), 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal(100))
                            .setScale(2, RoundingMode.HALF_UP)
                            .toString());
                }
                ratioList.add(bo);
            }
        }
        result.put("netProfit", netProfitList);
        result.put("ratio", ratioList);
        return result;
    }

    @Override
    public IndustryRankListVO industryRankList(final IndustryRankListQuery query) {
        final IndustryRankListVO result = new IndustryRankListVO();

        final String chainId = query.getChainId();
        DYNAMIC_TABLE_NAME_THREAD_LOCAL.set(chainId);
        final String regionId = query.getRegionId();

        // 支持省份和地级市查询
        String provinceName = null;
        String cityName = null;
        Integer regionLevel = null;
        Integer limit = 10; // 默认限制

        if (!StringUtils.isEmpty(regionId)) {
            final DmDivision division = dmDivisionMapper.selectById(regionId);
            if (division != null) {
                regionLevel = division.getLevel();
                if (regionLevel == 1) {
                    // 省份级别
                    provinceName = division.getName();
                    limit = query.getPageSize() != null ? query.getPageSize() : 100; // 省级默认100家
                } else if (regionLevel == 2) {
                    // 地级市级别
                    cityName = division.getName();
                    limit = query.getPageSize() != null ? query.getPageSize() : 50; // 地级市默认50家
                } else {
                    // 其他级别不支持
                    throw new BusinessException("不支持该级别的地区查询，仅支持省份(level=1)和地级市(level=2)");
                }
            }
        } else {
            // 未指定地区时使用传入的分页参数
            limit = query.getPageSize() != null ? query.getPageSize() : 10;
        }

        final String nodeId = query.getNodeId();
        List<String> childNodeIdList = new ArrayList<>();
        if (!StringUtils.isEmpty(nodeId)) {
            childNodeIdList = nodeService.getChildNodeIdList(chainId, nodeId);
        }

        final Integer type = query.getType();

        switch (type) {
            case 1:
                // 专利数量榜
                // 使用新的专利排名服务计算企业专利排名
                // 专利排名服务使用原有的regionName参数（省份或城市名称）
                final String regionNameForPatent = provinceName != null ? provinceName : cityName;
                final List<IndustryRankRdStrengthBO> rdStrengthList = patentRankingService.getPatentRankingByNode(chainId, childNodeIdList, regionNameForPatent, query.getApplicantType());
                result.setRdStrength(rdStrengthList);
                break;
            case 2:
                //投资潜力榜
                //执行指标-最新轮次融资金额
                final List<IndustryRankInvestPotentialBO> investPotentialList = industryRankListMapper.getInvestPotentialList(provinceName, cityName, childNodeIdList, limit);
                for (final IndustryRankInvestPotentialBO bo : investPotentialList) {
                    bo.setRegion(Constants.getBelongArea(bo.getProvince(), bo.getCity(), null));
                }
                result.setInvestPotentialList(investPotentialList);
                break;
            case 3:
                //资本青睐榜
                //执行指标-累计融资笔数
                final List<IndustryRankCapitalPreferenceBO> capitalPreferenceList = industryRankListMapper.getCapitalPreferenceList(provinceName, cityName, childNodeIdList, limit);
                for (final IndustryRankCapitalPreferenceBO bo : capitalPreferenceList) {
                    bo.setRegion(Constants.getBelongArea(bo.getProvince(), bo.getCity(), null));
                }
                result.setCapitalPreferenceList(capitalPreferenceList);
                break;
            case 4:
                //k8s环境不指定地区及节点（全链）时，需要11s多，单传节点和地区还能接受，所以将不指定地区及节点（全链）的数据缓存
                //对外投资榜
                //执行指标-对外投资企业数量
                //直接投资
                if (StringUtils.isEmpty(regionId) && StringUtils.isEmpty(nodeId)) {
                    final String key = "industryRank:" + chainId;
                    List<IndustryRankOutwardInvestBO> outwardInvestList = RedisUtils.getCacheObject(key);
                    if (CollectionUtils.isEmpty(outwardInvestList)) {
                        outwardInvestList = getOutwardInvest(null, null, null, 10);
                        result.setOutwardInvestList(outwardInvestList);
                        RedisUtils.setCacheObject(key, outwardInvestList, Duration.ofDays(30L));
                    }
                    result.setOutwardInvestList(outwardInvestList);
                } else {
                    final List<IndustryRankOutwardInvestBO> outwardInvestList = getOutwardInvest(provinceName, cityName, childNodeIdList, limit);
                    result.setOutwardInvestList(outwardInvestList);
                }

                break;
            case 5:
                //营收规模榜
                //执行指标-营业收入
                final List<IndustryRankAnnualReportBO> revenueScaleList = industryRankListMapper.getAnnualReportList(1, provinceName, cityName, childNodeIdList, limit);
                for (final IndustryRankAnnualReportBO bo : revenueScaleList) {
                    bo.setRegion(Constants.getBelongArea(bo.getProvince(), bo.getCity(), null));
                }
                result.setRevenueScaleList(revenueScaleList);
                break;
            case 6:
                //营收利润榜
                //执行指标-净利润
                final List<IndustryRankAnnualReportBO> revenueProfitList = industryRankListMapper.getAnnualReportList(2, provinceName, cityName, childNodeIdList, limit);
                for (final IndustryRankAnnualReportBO bo : revenueProfitList) {
                    bo.setRegion(Constants.getBelongArea(bo.getProvince(), bo.getCity(), null));
                }
                result.setRevenueProfitList(revenueProfitList);
                break;
            case 7:
                //链主企业榜
                //执行指标-总资产
                final List<IndustryRankAnnualReportBO> assetsTotalList = industryRankListMapper.getAnnualReportList(3, provinceName, cityName, childNodeIdList, limit);
                for (final IndustryRankAnnualReportBO bo : assetsTotalList) {
                    bo.setRegion(Constants.getBelongArea(bo.getProvince(), bo.getCity(), null));
                }
                result.setAssetsTotalList(assetsTotalList);
                break;
            default:
                break;
        }

        // 设置分页和地区信息
        result.setPageNum(query.getPageNum());
        result.setPageSize(limit);
        result.setRegionLevel(regionLevel);
        if (provinceName != null) {
            result.setRegionName(provinceName);
        } else if (cityName != null) {
            result.setRegionName(cityName);
        }

        return result;
    }

    private List<IndustryRankOutwardInvestBO> getOutwardInvest(final String provinceName, final String cityName, final List<String> childNodeIdList, final Integer limit) {
        //直接投资
        final List<IndustryRankOutwardInvestBO> outwardDirectInvestList = industryRankListMapper.getOutwardDirectInvestList(provinceName, cityName, childNodeIdList, limit);
        if (!CollectionUtils.isEmpty(outwardDirectInvestList)) {
            //地区名称
            final List<DmDivision> divisionList = dmDivisionMapper.selectList(Wrappers.<DmDivision>query()
                    .in("level", 1, 2));
            final Map<String, DmDivision> divisionMap = new HashMap<>();
            for (final DmDivision division : divisionList) {
                divisionMap.put(division.getId(), division);
            }
            //控股企业数
            final List<String> cidList = outwardDirectInvestList.stream()
                    .map(IndustryRankOutwardInvestBO::getCid)
                    .collect(Collectors.toList());
            final List<IndustryRankOutwardInvestBO> holdingCountList = industryRankListMapper.getHoldingCompanyCountList(cidList);
            final Map<String, IndustryRankOutwardInvestBO> outwardIndirectInvestMap = new HashMap<>();
            for (final IndustryRankOutwardInvestBO bo : holdingCountList) {
                outwardIndirectInvestMap.put(bo.getCid(), bo);
            }
            for (final IndustryRankOutwardInvestBO bo : outwardDirectInvestList) {
                bo.setProvince(divisionMap.get("division/" + bo.getProvince()) == null ? null : divisionMap.get("division/" + bo.getProvince()).getName());
                bo.setCity(divisionMap.get("division/" + bo.getCity()) == null ? null : divisionMap.get("division/" + bo.getCity()).getName());
                bo.setHoldingCount(outwardIndirectInvestMap.get(bo.getCid()) == null ? 0 : outwardIndirectInvestMap.get(bo.getCid()).getHoldingCount());

                bo.setRegion(Constants.getBelongArea(bo.getProvince(), bo.getCity(), null));
            }
        }

        return outwardDirectInvestList;
    }

    @Override
    public File industryRankListExport(final IndustryRankListQuery query, final HttpServletResponse response,
                                       final Long sysUserDownloadId) throws IOException {
        final IndustryRankListVO vo = industryRankList(query);
        final Integer type = query.getType();
        final List<Object> dataList = new ArrayList<>();
        Class<?> clazz = null;
        switch (type) {
            case 1:
                final List<IndustryRankRdStrengthBO> list1 = vo.getRdStrength();
                for (final IndustryRankRdStrengthBO bo : list1) {
                    final IndustryRankRdStrengthExport export = new IndustryRankRdStrengthExport();
                    BeanUtils.copyProperties(bo, export);
                    dataList.add(export);
                }
                clazz = IndustryRankRdStrengthExport.class;
                break;
            case 2:
                final List<IndustryRankInvestPotentialBO> list2 = vo.getInvestPotentialList();
                for (final IndustryRankInvestPotentialBO bo : list2) {
                    final IndustryRankInvestPotentialExport export = new IndustryRankInvestPotentialExport();
                    BeanUtils.copyProperties(bo, export);
                    dataList.add(export);
                }
                clazz = IndustryRankInvestPotentialExport.class;
                break;
            case 3:
                final List<IndustryRankCapitalPreferenceBO> list3 = vo.getCapitalPreferenceList();
                for (final IndustryRankCapitalPreferenceBO bo : list3) {
                    final IndustryRankCapitalPreferenceExpert export = new IndustryRankCapitalPreferenceExpert();
                    BeanUtils.copyProperties(bo, export);
                    dataList.add(export);
                }
                clazz = IndustryRankCapitalPreferenceExpert.class;
                break;
            case 4:
                final List<IndustryRankOutwardInvestBO> list4 = vo.getOutwardInvestList();
                for (final IndustryRankOutwardInvestBO bo : list4) {
                    final IndustryRankOutwardInvestExport export = new IndustryRankOutwardInvestExport();
                    BeanUtils.copyProperties(bo, export);
                    dataList.add(export);
                }
                clazz = IndustryRankOutwardInvestExport.class;
                break;
            case 5:
                final List<IndustryRankAnnualReportBO> list5 = vo.getRevenueScaleList();
                for (final IndustryRankAnnualReportBO bo : list5) {
                    final IndustryRankRevenueScaleExport export = new IndustryRankRevenueScaleExport();
                    BeanUtils.copyProperties(bo, export);
                    dataList.add(export);
                }
                clazz = IndustryRankRevenueScaleExport.class;
                break;
            case 6:
                final List<IndustryRankAnnualReportBO> list6 = vo.getRevenueProfitList();
                for (final IndustryRankAnnualReportBO bo : list6) {
                    final IndustryRankRevenueProfitExport export = new IndustryRankRevenueProfitExport();
                    BeanUtils.copyProperties(bo, export);
                    dataList.add(export);
                }
                clazz = IndustryRankRevenueProfitExport.class;
                break;
            case 7:
                final List<IndustryRankAnnualReportBO> list7 = vo.getAssetsTotalList();
                for (final IndustryRankAnnualReportBO bo : list7) {
                    final IndustryRankAssetsTotalExport export = new IndustryRankAssetsTotalExport();
                    BeanUtils.copyProperties(bo, export);
                    dataList.add(export);
                }
                clazz = IndustryRankAssetsTotalExport.class;
                break;
            default:
                break;
        }

        // 设置系统属性以允许访问外部样式表
        System.setProperty("javax.xml.accessExternalStylesheet", "all");
        final Date today = new Date();//今日日期
        final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        final String dateStr = sdf.format(today);
        final String excelName = "产业榜单导出表";
        final String fileName = excelName + "_" + dateStr + ".xlsx";
        // 标题样式
        final WriteCellStyle headWriteCellStyle = ExcelStyleUtil.getHeadStyle();
        // 内容样式
        final WriteCellStyle contentWriteCellStyle = ExcelStyleUtil.getContentStyleNew();
        final HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        final ExcelWriter excelWriter;
        if (response != null) {
            // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
            excelWriter = EasyExcel.write(response.getOutputStream())
                    .registerWriteHandler(horizontalCellStyleStrategy).registerWriteHandler(new CustomHandler()).build();
            final WriteSheet writeSheet = EasyExcel.writerSheet(0, "产业榜单列表").head(clazz).build();
            excelWriter.write(dataList, writeSheet);
            excelWriter.finish();
            return null;
        } else {
            // 保存当次的记录
            final File tempFile = new File(getActualFilePath("temp", fileName));
            excelWriter = EasyExcel.write(tempFile)
                    .registerWriteHandler(horizontalCellStyleStrategy).registerWriteHandler(new CustomHandler()).build();
            final WriteSheet writeSheet = EasyExcel.writerSheet(0, "产业榜单列表").head(clazz).build();
            excelWriter.write(dataList, writeSheet);
            excelWriter.finish();
            return tempFile;
        }
    }
}
