package com.quantchi.knowledge.center.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.quantchi.knowledge.center.bean.bo.IndustryRankRdStrengthBO;
import com.quantchi.knowledge.center.bean.constant.CacheConstants;
import com.quantchi.knowledge.center.bean.constant.Constants;
import com.quantchi.knowledge.center.bean.entity.Company;
import com.quantchi.knowledge.center.bean.enums.EsIndexEnum;
import com.quantchi.knowledge.center.bean.vo.NameCountVO;
import com.quantchi.knowledge.center.dao.mysql.PatentRankingMapper;
import com.quantchi.knowledge.center.helper.ElasticsearchHelper;
import com.quantchi.knowledge.center.service.ICompanyService;
import com.quantchi.knowledge.center.service.PatentRankingService;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 专利排名服务实现类
 * 实现新的专利数量榜计算逻辑
 */
@Service
@Slf4j
public class PatentRankingServiceImpl implements PatentRankingService {

    @Autowired
    private PatentRankingMapper patentRankingMapper;

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private ICompanyService companyService;

    // 在XML中已设置结果限制为10条

    /**
     * 根据产业链节点获取企业专利排名
     * 当nodeIdList和provinceName、cityName都为空时使用缓存
     *
     * @param chainId    产业链ID
     * @param nodeIdList 节点ID列表
     * @param provinceName 省份名称
     * @param cityName 城市名称
     * @return 企业专利排名列表
     */
    @Override
    @Cacheable(value = CacheConstants.PATENT_RANKING_CACHE, condition = "(#nodeIdList == null || #nodeIdList.isEmpty()) && (#provinceName == null || #provinceName.isEmpty()) && (#cityName == null || #cityName.isEmpty()) && (#type == null || #type.isEmpty())")
    public List<IndustryRankRdStrengthBO> getPatentRankingByNode(final String chainId, final List<String> nodeIdList, final String provinceName, final String cityName, final String type) {
        try {
            final String applicantType = CharSequenceUtil.isEmpty(type) ? "company" : type;
            log.info("获取专利排名，nodeIdList为空：{}，provinceName为空：{}，cityName为空：{}，类型：{}",
                    CollectionUtils.isEmpty(nodeIdList), CharSequenceUtil.isEmpty(provinceName), CharSequenceUtil.isEmpty(cityName), applicantType);

            // 当chainId为schema_icd时，从ES中获取数据
            if ("schema_icd".equals(chainId)) {
                // 对于ES查询，优先使用省份名称，如果省份为空则使用城市名称
                final String regionNameForES = CharSequenceUtil.isNotBlank(provinceName) ? provinceName : cityName;
                return getPatentRankingFromES(nodeIdList, regionNameForES, applicantType);
            }

            // 使用新的查询方法，一次性获取专利总量和授权数量
            final List<IndustryRankRdStrengthBO> result = patentRankingMapper.getCompanyPatentRankingWithAccreditByNode(nodeIdList, provinceName, cityName, applicantType);

            // 批量设置企业名称
            setCompanyNames(result);

            // 只需要设置地区信息
            for (final IndustryRankRdStrengthBO bo : result) {
                bo.setRegion(Constants.getBelongArea(bo.getProvince(), bo.getCity(), null));
            }

            return result;
        } catch (Exception e) {
            log.error("获取专利排名失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 从ES中获取专利排名数据
     *
     * @param nodeIdList    产业链节点ID列表，对应ES中的icd字段
     * @param regionName    地区名称，对应ES中的province字段
     * @param applicantType 申请人类型，“company”表示企业，“university”表示高校
     * @return 专利排名列表
     */
    private List<IndustryRankRdStrengthBO> getPatentRankingFromES(final List<String> nodeIdList, final String regionName, final String applicantType) {
        try {
            // 构建查询条件
            final BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

            // 添加icd过滤条件
            if (!CollectionUtils.isEmpty(nodeIdList)) {
                boolQueryBuilder.filter(QueryBuilders.termsQuery("icd", nodeIdList));
            }

            // 添加province过滤条件
            if (CharSequenceUtil.isNotBlank(regionName)) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("province", regionName));
            }

            // 添加申请人类型过滤条件
            if ("university".equals(applicantType)) {
                // 高校类型：名称以"-e"结尾或包含"大学"、"学院"、"university"
                final BoolQueryBuilder applicantNameQuery = QueryBuilders.boolQuery();
                applicantNameQuery.should(QueryBuilders.wildcardQuery("patentees.name", "*-e"));
                applicantNameQuery.should(QueryBuilders.wildcardQuery("patentees.name", "*-t"));
                applicantNameQuery.should(QueryBuilders.wildcardQuery("patentees.name", "*research institute"));
                applicantNameQuery.should(QueryBuilders.wildcardQuery("patentees.name", "*大学"));
                applicantNameQuery.should(QueryBuilders.wildcardQuery("patentees.name", "*学院"));
                applicantNameQuery.should(QueryBuilders.wildcardQuery("patentees.name", "*研究院"));
                applicantNameQuery.should(QueryBuilders.wildcardQuery("patentees.name", "*研究所"));
                applicantNameQuery.should(QueryBuilders.wildcardQuery("patentees.name", "*university*"));
                boolQueryBuilder.filter(applicantNameQuery);
            } else {
                // 企业类型：名称不以"-e"结尾且不包含"大学"、"学院"、"university"
                final BoolQueryBuilder applicantNameQuery = QueryBuilders.boolQuery();
                applicantNameQuery.mustNot(QueryBuilders.wildcardQuery("patentees.name", "*-e"));
                applicantNameQuery.mustNot(QueryBuilders.wildcardQuery("patentees.name", "*-t"));
                applicantNameQuery.mustNot(QueryBuilders.wildcardQuery("patentees.name", "*research institute"));
                applicantNameQuery.mustNot(QueryBuilders.wildcardQuery("patentees.name", "*大学"));
                applicantNameQuery.mustNot(QueryBuilders.wildcardQuery("patentees.name", "*学院"));
                applicantNameQuery.mustNot(QueryBuilders.wildcardQuery("patentees.name", "*研究院"));
                applicantNameQuery.mustNot(QueryBuilders.wildcardQuery("patentees.name", "*研究所"));
                applicantNameQuery.mustNot(QueryBuilders.wildcardQuery("patentees.name", "*university*"));
                boolQueryBuilder.filter(applicantNameQuery);
            }

            // patentees.id分组，获取企业专利总数
            // 初始查询更多记录(30条)，因为部分企业ID可能在company表中没有对应记录
            final List<NameCountVO> companyPatentCountList = elasticsearchHelper.getTermBucketsAggregation(
                    EsIndexEnum.PATENT.getEsIndex(), "patentees.id", boolQueryBuilder, 50);

            // 创建结果列表
            final List<IndustryRankRdStrengthBO> resultList = new ArrayList<>();

            // 计数器，记录有效的结果数量
            int validResultCount = 0;
            // 目标返回结果数量
            final int targetResultCount = 10;

            // 处理每个专利权人的专利情况
            for (final NameCountVO nameCountVO : companyPatentCountList) {
                final String companyId = nameCountVO.getName();
                final long totalCount = nameCountVO.getCount();

                // 首先获取企业名称和所在地区
                final Map<String, Object> companyInfo = elasticsearchHelper.getDataById(
                        EsIndexEnum.COMPANY.getEsIndex(),
                        companyId,
                        new String[]{"name", "province", "city"},
                        null);

                // 如果企业信息为空，则跳过
                if (companyInfo == null || companyInfo.isEmpty()) {
                    log.debug("未找到企业信息: {}", companyId);
                    continue;
                }

                // 检查企业所在省份是否与筛选条件匹配
                if (CharSequenceUtil.isNotBlank(regionName)) {
                    final String companyProvince = (String) companyInfo.get("province");
                    if (!Objects.equals(regionName, companyProvince)) {
                        log.debug("企业所在省份与筛选条件不匹配，跳过: {}, 企业省份: {}, 筛选省份: {}", 
                                companyId, companyProvince, regionName);
                        continue;
                    }
                }

                // 获取企业名称
                final String companyName = (String) companyInfo.get("name");
                if (companyName == null || companyName.isEmpty()) {
                    log.debug("企业名称为空: {}", companyId);
                    continue;
                }

                // 判断是否为高校类型（使用与查询条件相同的判断标准）
                boolean isUniversity = false;

                // 检查名称是否以"-e"或"-t"结尾
                if (companyName.endsWith("-e") || companyName.endsWith("-t")) {
                    isUniversity = true;
                    log.debug("名称以-e或-t结尾，判断为高校: {}", companyName);
                }

                // 检查名称是否包含特定关键词
                if (!isUniversity) {
                    final String[] keywords = {
                        "research institute", "大学", "学院", "研究院", "研究所", "university"
                    };

                    for (final String keyword : keywords) {
                        if (companyName.endsWith(keyword)) {
                            isUniversity = true;
                            log.debug("名称包含关键词，判断为高校: {}, 关键词: {}", companyName, keyword);
                            break;
                        }
                    }
                }

                // 根据 applicantType 参数决定是否跳过
                if ("university".equals(applicantType)) {
                    // 如果要查询高校，但当前不是高校，则跳过
                    if (!isUniversity) {
                        log.debug("查询高校但当前专利权人不是高校，跳过: {}", companyName);
                        continue;
                    }
                    log.info("处理高校专利数据: {}, ID: {}", companyName, companyId);
                } else {
                    // 如果要查询企业，但当前是高校，则跳过
                    if (isUniversity) {
                        log.debug("查询企业但当前专利权人是高校，跳过: {}", companyName);
                        continue;
                    }
                    log.info("处理企业专利数据: {}, ID: {}", companyName, companyId);
                }

                // 构建查询授权专利的条件
                final BoolQueryBuilder accreditQueryBuilder = QueryBuilders.boolQuery();
                accreditQueryBuilder.filter(QueryBuilders.termQuery("patentees.id", companyId));
                accreditQueryBuilder.filter(QueryBuilders.termQuery("patent_type", "发明授权"));

                // 如果有icd过滤条件，添加到授权查询中
                if (!CollectionUtils.isEmpty(nodeIdList)) {
                    accreditQueryBuilder.filter(QueryBuilders.termsQuery("icd", nodeIdList));
                }

                // 如果有province过滤条件，添加到授权查询中
                if (CharSequenceUtil.isNotBlank(regionName)) {
                    accreditQueryBuilder.filter(QueryBuilders.termQuery("province", regionName));
                }

                // 获取授权专利数量
                final long accreditCount = elasticsearchHelper.countRequest(EsIndexEnum.PATENT.getEsIndex(), accreditQueryBuilder);

                final IndustryRankRdStrengthBO bo = new IndustryRankRdStrengthBO();
                bo.setCid(companyId);

                if (companyInfo != null && !companyInfo.isEmpty()) {
                    bo.setCompanyName((String) companyInfo.get("name"));
                    bo.setProvince((String) companyInfo.get("province"));
                    bo.setCity((String) companyInfo.get("city"));
                    bo.setRegion(Constants.getBelongArea(bo.getProvince(), bo.getCity(), null));
                }

                bo.setTotalCount((int) totalCount);
                bo.setAccreditCount((int) accreditCount);

                // 只添加有企业信息的记录，并且限制返回结果数量
                if (companyInfo != null && !companyInfo.isEmpty()) {
                    resultList.add(bo);
                    validResultCount++;
                }

                // 如果已经找到足够的有效结果，提前结束循环
                if (validResultCount >= targetResultCount) {
                    break;
                }
            }

            return resultList;
        } catch (Exception e) {
            log.error("从ES获取企业专利排名失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 批量设置企业名称
     *
     * @param resultList 专利排名结果列表
     */
    private void setCompanyNames(final List<IndustryRankRdStrengthBO> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        try {
            // 提取所有企业ID
            final List<String> companyIds = resultList.stream()
                    .map(IndustryRankRdStrengthBO::getCid)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(companyIds)) {
                log.warn("企业ID列表为空，无法批量查询企业名称");
                return;
            }

            // 批量查询企业信息
            final LambdaQueryWrapper<Company> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(Company::getId, Company::getName)
                    .in(Company::getId, companyIds);
            final List<Company> companies = companyService.list(queryWrapper);

            // 构建企业ID到企业名称的映射
            final Map<String, String> companyNameMap = companies.stream()
                    .collect(Collectors.toMap(Company::getId, Company::getName));

            // 设置企业名称
            for (final IndustryRankRdStrengthBO bo : resultList) {
                final String companyName = companyNameMap.get(bo.getCid());
                if (companyName != null) {
                    bo.setCompanyName(companyName);
                } else {
                    log.warn("未找到企业ID为 {} 的企业名称", bo.getCid());
                }
            }

            log.info("成功为 {} 条专利排名记录设置企业名称", resultList.size());
        } catch (Exception e) {
            log.error("批量设置企业名称失败", e);
        }
    }

}
