#投研本地测试环境
server:
  port: 8612
  tomcat:
    uri-encoding: UTF-8
    max-http-form-post-size: -1
    max-swallow-size: -1
  max-http-header-size: 1024000
  undertow:
    max-http-post-size: -1
spring:
  application:
    name: icir-test
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        master:
          url: ***************************************************************************************************************************************************************************************************************************************************************************
          username: icirdb
          password: gSy<PERSON>@a!T3GUf2Uv
          driverClassName: com.mysql.cj.jdbc.Driver
        sys:
          type: com.zaxxer.hikari.HikariDataSource
          url: ***************************************************************************************************************************************************************************************************************************************************************************
          username: icirdb
          password: gSyBA@a!T3GUf2Uv
          driverClassName: com.mysql.cj.jdbc.Driver

    # 连接池配置
    hikari:
      # 最小空闲连接数
      minimum-idle: 10
      # 最大连接数 - 增加到50个
      maximum-pool-size: 50
      # 空闲连接超时时间，默认600000（10分钟）
      idle-timeout: 600000
      # 连接最大存活时间，0表示永久存活，默认1800000（30分钟）
      max-lifetime: 1800000
      # 连接超时时间，默认30000（30秒）
      connection-timeout: 30000
      # 用于测试连接是否可用的查询语句
      connection-test-query: SELECT 1
      # 连接泄漏检测阈值，0表示禁用，建议设置为60000（1分钟）
      leak-detection-threshold: 60000
      # 是否自动提交事务
      auto-commit: true
      # 连接池名称
      pool-name: HikariCP-ICIR
      # 连接池维护周期，默认30000毫秒（30秒），可以适当增加减少维护频率
      housekeeping-period-ms: 60000

  servlet:
    multipart:
      max-file-size: -1
      max-request-size: -1
  # redis配置
  redis:
    database: 6
    host: ************
    port: 60379
    password: qS$#ON1z6Ns1g&FJ
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
#es:
#  cluster:
#    hosts: ************:8199
#  username: elastic
#  password: C8hLrU%L4yD4*C2K

#测试环境使用正式环境的 es 库，验证查询准确性
es:
  cluster:
    hosts: es-cn-2nx49ub4600023f1z.public.elasticsearch.aliyuncs.com:9200
  username: elastic
  password: '*dnKe8GTRav7Zkq4'

mybatis-plus:
  mapper-locations: classpath:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.quantchi.knowledge.center.entity
  global-config:
    # 数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID",ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: auto
      #字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      field-strategy: not_empty
      #驼峰下划线转换
      table-underline: true
      db-type: mysql
  # 原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true


logging:
  config: classpath:logback-spring.xml
  level:
    # 关闭HikariCP的DEBUG日志，避免过多连接池维护日志
    com.zaxxer.hikari: INFO
    # 开启MyBatis的DEBUG日志，便于监控SQL执行
#    com.quantchi.knowledge.center.dao.mysql: DEBUG

file:
  preUrl: http://192.168.1.68:30897

interface:
  rate: 1

# Sa-Token配置
sa-token:
  # token 名称 (同时也是cookie名称)
  token-name: Authorization
  # token 有效期，单位s, -1代表永不过期
  timeout: -1
  # token 临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  activity-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: simple-uuid
  # 是否输出操作日志
  is-log: false
  maxLoginCount: 100
  is-read-cookie: true    # 是否从cookie中读取token
  is-read-header: true    # 是否从header中读取token

log:
  print:
    flag: true

report:
  url: 'http://icir-reporter:8331/model/generate_report'
data:
  prefix: 'http://icir-data:8353/data'

oss:
  profiles:
    active: test

# 存储类型选择：oss或minio
storage:
  type: oss

