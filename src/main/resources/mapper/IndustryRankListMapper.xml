<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quantchi.knowledge.center.dao.mysql.IndustryRankListMapper">


    <select id="getCompanyPatentAccreditCountList" resultType="com.quantchi.knowledge.center.bean.bo.IndustryRankRdStrengthBO">
        SELECT applicant_id cid, COUNT(DISTINCT patent_id) AS accreditCount
        FROM patent_applicant
        WHERE patent_type = '发明授权'
        GROUP BY applicant_id
    </select>

    <select id="getCompanyPatentTotalCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT patent_id) count
        FROM patent_applicant
        WHERE is_valid = 1
        AND applicant_id = #{cid}
    </select>

    <select id="getInvestPotentialList" resultType="com.quantchi.knowledge.center.bean.bo.IndustryRankInvestPotentialBO">
        SELECT
            a.cid,
            a.financing_company companyName,
            a.financing_date,
            Round(a.financing_value_cal/*********, 2) financingValueCal,
            a.financing_round_cal financingRoundCal,
            a.province,
            a.city
        FROM company_financing a
        INNER JOIN (
            SELECT
                cid,
                MAX(financing_date) AS max_financing_date
            FROM company_financing
            WHERE cid IN (
                SELECT distinct cid
                FROM company_node
                WHERE 1=1
                <if test="childNodeIdList != null and childNodeIdList.size() != 0">
                    AND node_id IN (
                        <foreach collection="childNodeIdList" separator="," item="nodeId">
                            #{nodeId}
                        </foreach>
                    )
                </if>
                <if test="province != null and province != ''">
                    AND province = #{province}
                </if>
                <if test="city != null and city != ''">
                    AND city = #{city}
                </if>
            )
            GROUP BY cid
        ) b ON a.cid = b.cid AND a.financing_date = b.max_financing_date
        ORDER BY a.financing_value_cal DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
        <if test="limit == null or limit <= 0">
            LIMIT 10
        </if>
    </select>

    <select id="getCapitalPreferenceList" resultType="com.quantchi.knowledge.center.bean.bo.IndustryRankCapitalPreferenceBO">
        SELECT
            cid, financing_company companyName, count(1) financingCount, ROUND(sum(financing_value_cal)/*********, 2) financingValueCalTotal,
            province, city
        FROM company_financing
        WHERE cid IN (
            SELECT distinct cid
            FROM company_node
            WHERE 1=1
            <if test="childNodeIdList != null and childNodeIdList.size() != 0">
                AND node_id IN (
                    <foreach collection="childNodeIdList" separator="," item="nodeId">
                        #{nodeId}
                    </foreach>
                )
            </if>
            <if test="province != null and province != ''">
                AND province = #{province}
            </if>
            <if test="city != null and city != ''">
                AND city = #{city}
            </if>
        )
        GROUP BY cid
        ORDER BY financingValueCalTotal DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
        <if test="limit == null or limit <= 0">
            LIMIT 10
        </if>
    </select>

    <select id="getOutwardDirectInvestList" resultType="com.quantchi.knowledge.center.bean.bo.IndustryRankOutwardInvestBO">
        SELECT
	        cid,
	        company_name companyName,
	        COUNT( DISTINCT invested_name ) directCount,
	        province_code province,
	        city_code city
        FROM
	        company_invest
        WHERE
	        cid IN (
	            SELECT
	                DISTINCT cid
	            FROM company_node
	            WHERE
	                1=1
	                <if test="childNodeIdList != null and childNodeIdList.size() != 0">
                        AND node_id IN (
                            <foreach collection="childNodeIdList" item="nodeId" separator=",">
                                #{nodeId}
                            </foreach>
                        )
                    </if>
                    <if test="province != null and province != ''">
                        AND province = #{province}
                    </if>
                    <if test="city != null and city != ''">
                        AND city = #{city}
                    </if>
            )
        GROUP BY
	        cid
        ORDER BY
	        directCount DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
        <if test="limit == null or limit <= 0">
            LIMIT 10
        </if>
    </select>

    <select id="getHoldingCompanyCountList" resultType="com.quantchi.knowledge.center.bean.bo.IndustryRankOutwardInvestBO">
        SELECT cid, COUNT(DISTINCT invested_name) holdingCount
        FROM company_invest
        WHERE cid IN (
            <foreach collection="cidList" separator="," item="cid">
                #{cid}
            </foreach>
        ) AND invest_ratio &gt; 50
        GROUP BY cid
    </select>

    <select id="getAnnualReportList" resultType="com.quantchi.knowledge.center.bean.bo.IndustryRankAnnualReportBO">
        SELECT DISTINCT a.*, CONCAT(a.year, '-12-31') date, b.province, b.city
        FROM (
            SELECT cid, name companyName, year,
            <if test="type == 1">
                ROUND(gross_income/10000, 2) grossIncome
            </if>
            <if test="type == 2">
                ROUND(net_profit/10000, 2) netProfit
            </if>
            <if test="type == 3">
                ROUND(total_assets/10000, 2) totalAssets
            </if>
            FROM company_finance
            WHERE year = (SELECT MAX(year) FROM company_finance WHERE quarter = 4) AND quarter = 4
            AND cid IN (
                SELECT DISTINCT cid
                FROM company_node
                WHERE 1=1
                <if test="childNodeIdList != null and childNodeIdList.size() != 0">
                    AND node_id IN (
                        <foreach collection="childNodeIdList" separator="," item="nodeId">
                            #{nodeId}
                        </foreach>
                    )
                </if>
                <if test="province != null and province != ''">
                    AND province = #{province}
                </if>
                <if test="city != null and city != ''">
                    AND city = #{city}
                </if>
            )
            <if test="type == 1">
                ORDER BY gross_income DESC
            </if>
            <if test="type == 2">
                ORDER BY net_profit DESC
            </if>
            <if test="type == 3">
                ORDER BY total_assets DESC
            </if>
            <if test="limit != null and limit > 0">
                LIMIT #{limit}
            </if>
            <if test="limit == null or limit <= 0">
                LIMIT 10
            </if>
        ) a LEFT JOIN company_node b ON a.cid = b.cid
    </select>
</mapper>