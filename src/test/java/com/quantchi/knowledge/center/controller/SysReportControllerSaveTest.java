package com.quantchi.knowledge.center.controller;

import com.quantchi.knowledge.center.bean.system.bo.SysReportSaveBO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SysReportController 保存接口测试
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@SpringBootTest
public class SysReportControllerSaveTest {

    @Test
    public void testSysReportSaveBOCreation() {
        // 测试 SysReportSaveBO 对象创建
        SysReportSaveBO saveBO = new SysReportSaveBO();
        saveBO.setReportId(1L);
        saveBO.setTitle("测试报告标题");
        saveBO.setPreserveData("<html><body>测试报告内容</body></html>");

        // 验证字段设置
        assertEquals(1L, saveBO.getReportId());
        assertEquals("测试报告标题", saveBO.getTitle());
        assertEquals("<html><body>测试报告内容</body></html>", saveBO.getPreserveData());
    }

    @Test
    public void testSysReportSaveBOValidation() {
        // 测试必填字段验证
        SysReportSaveBO saveBO = new SysReportSaveBO();
        
        // reportId 是必填字段，应该有 @NotNull 注解
        assertNull(saveBO.getReportId());
        
        // 设置必填字段
        saveBO.setReportId(1L);
        assertNotNull(saveBO.getReportId());
    }
}
